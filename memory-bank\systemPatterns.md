# System Patterns

## Coding Patterns

### Modular Design Principles
- **Single Responsibility**: Each module handles one specific task (document extraction, AI processing, file generation, project management)
- **High Cohesion, Low Coupling**: Related functions are centralized, reducing dependencies between modules
- **Stable Interfaces**: Expose stable interfaces externally while internal implementations can vary
- **Format Extensibility**: New document formats can be added without affecting existing functionality

### Naming Conventions
- **Snake Case**: Use snake_case for Python variables and functions
- **Semantic Naming**: Variable/function names should clearly express their purpose
- **Project Naming**: `{cleaned_filename}_{timestamp}` format for unique identification
- **Image Naming**: `{project}_{source}_{page:03d}_img_{index:03d}.png` for standardization
- **Image Folder Naming**: `images_{cleaned_document_name}` for Windows compatibility

### Error Handling Patterns
- **Graceful Degradation**: System continues processing other documents when one fails
- **Comprehensive Logging**: Detailed logging with different levels (DEBUG, INFO, WARNING, ERROR)
- **User-Friendly Messages**: Clear error messages with actionable guidance
- **Retry Mechanisms**: Exponential backoff for API calls and network operations
- **Optional Feature Fallback**: Intelligent detection and graceful fallback for missing optional dependencies

### Configuration Management
- **Centralized Config**: Use config.ini for all configurable parameters
- **Environment Variables**: Support environment variable overrides for sensitive data
- **Default Values**: Provide sensible defaults for all configuration options
- **Validation**: Validate configuration parameters at startup
- **Template Management**: JSON-based template storage with enum type safety and metadata support

## Architectural Patterns

### Document Processing Pipeline
```
Input Document → Format Detection → Extractor Selection → Content Processing → Output Generation
```
- **Format Detection**: Automatic identification based on file extensions
- **Extractor Pattern**: Separate extractor classes for each document format
- **Unified Interface**: Common API across all document types
- **Pipeline Processing**: Consistent processing flow regardless of input format

### GUI Application Architecture
```
Main Application → Feature Detection → Component Initialization → Event Handling → Backend Integration
```
- **Progressive Enhancement**: Core functionality + optional enhancements based on available dependencies
- **Smart Initialization**: Automatic detection of optional libraries with graceful fallback
- **Unified Entry Point**: Single application file with intelligent feature branching
- **Threaded Processing**: Background operations with real-time UI updates

### Prompt Management Architecture
```
Template Storage → Template Loading → Validation → Selection Interface → Runtime Application
```
- **JSON Template Storage**: Structured storage with metadata and type information
- **Enum Type Safety**: QuestionType and AnswerFormat enums for type validation
- **Dynamic Template Selection**: Runtime template switching with validation
- **Multi-Interface Support**: Unified template management for CLI and GUI interfaces
- **Backward Compatibility**: Graceful fallback to default templates when specified templates unavailable

### Project-Based Organization
```
documents/
├── source/           # Original documents
├── extracted/        # Processed content by project
│   └── [project]/
├── anki/            # Generated flashcards
├── cache/[project]/ # Project-specific cache
└── logs/            # Processing logs
```
- **Isolation**: Each project has independent workspace
- **Resource Management**: Proper cleanup and memory management
- **Caching Strategy**: Project-specific caching for resume capability
- **Lifecycle Management**: Complete project creation, processing, and cleanup

### AI Integration Pattern
```
Text Input → Chunking → Concurrent Processing → Validation → Output Assembly
```
- **Sliding Window**: Configurable chunk size and stride for optimal AI processing
- **Concurrent Processing**: ThreadPoolExecutor for parallel API calls
- **Streaming Support**: Handle large responses efficiently
- **Cross-Chunk Validation**: Ensure completeness across processing boundaries

### Content Filtering Architecture
```
Content Input → Similarity Detection → Threshold Comparison → Filter/Archive Decision
```
- **Template Matching**: Compare against known spam image templates
- **Multiple Algorithms**: Histogram, perceptual hashing, SSIM comparison
- **Configurable Thresholds**: Adjustable similarity thresholds (0.0-1.0)
- **Archive System**: Save filtered content with similarity scores for review

### Cache Progress Calculation Pattern
```
Markdown File → Chunk Calculation → Cache File Count → Progress Ratio → Status Display
```
- **Expected Chunk Calculation**: Use same sliding window logic as make_chunks function
- **Actual Cache Counting**: Count numeric-named JSON files in cache directory
- **Progress Ratio**: Calculate cached/total percentage with meaningful status messages
- **Smart Status Display**: Context-aware status messages (未缓存, X/Y (Z%), 已完成)

### Batch Operation Pattern
```
Multi-Selection → Confirmation Dialog → Background Processing → Progress Tracking → Result Summary
```
- **Multi-Select Support**: Extended selection mode with visual feedback
- **Batch Confirmation**: Show selected items with truncation for large lists
- **Concurrent Processing**: Process multiple items with individual error isolation
- **Progress Tracking**: Real-time progress updates with current item display
- **Result Aggregation**: Comprehensive success/failure statistics with detailed results

## Testing Patterns

### Validation Testing
- **End-to-End Testing**: Complete pipeline validation with real documents
- **Format-Specific Testing**: Dedicated tests for each document format
- **Edge Case Testing**: Handle corrupted files, empty documents, large files
- **Performance Testing**: Validate processing speed and memory usage

### Content Quality Assurance
- **Image Preservation Testing**: Verify all images are correctly extracted and positioned
- **Format Preservation Testing**: Ensure original formatting is maintained in output
- **Cross-Chunk Validation**: Test content completeness across processing boundaries
- **Similarity Threshold Testing**: Validate content filtering accuracy

### Integration Testing
- **API Integration**: Test AI service integration with different endpoints
- **File System Testing**: Validate Windows compatibility and path handling
- **Configuration Testing**: Test various configuration combinations
- **Error Recovery Testing**: Validate system behavior under failure conditions

### User Experience Testing
- **CLI Interface Testing**: Validate all command-line options and error messages
- **Fuzzy Matching Testing**: Test project name matching with various inputs
- **Interactive Mode Testing**: Validate user interaction flows
- **Progress Reporting Testing**: Ensure accurate progress tracking and display

## Code Quality Standards

### Python Best Practices
- **PEP 8 Compliance**: Follow Python style guidelines
- **Type Hints**: Use type hints for better code documentation and IDE support
- **Docstrings**: Comprehensive documentation for all public functions and classes
- **Context Managers**: Use context managers for resource management

### Performance Optimization
- **Memory Management**: Efficient handling of large documents
- **Concurrent Processing**: Utilize multiple threads for I/O-bound operations
- **Caching Strategy**: Multi-level caching to avoid redundant processing
- **Resource Cleanup**: Proper cleanup of temporary files and resources

### Security Considerations
- **API Key Protection**: Never hardcode sensitive information
- **Input Validation**: Validate all user inputs and file paths
- **Path Traversal Prevention**: Secure file system access
- **Error Information Protection**: Avoid exposing sensitive data in error messages

2025-06-15 15:09:57 - Log of updates made.
2025-06-15 15:35:00 - Migrated system patterns and standards from .codelf directory
2025-06-15 16:04:02 - Added GUI application architecture patterns and progressive enhancement strategies